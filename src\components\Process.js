"use client";

import { motion } from 'framer-motion';
import { useRef, useState, useEffect } from 'react';
import AnimatedTitle from './AnimatedTitle';

const ProcessCard = ({ number, title, description, shouldShow }) => {
  return (
    <motion.div
      className="w-full"
      initial={{ opacity: 0, y: 50 }}
      animate={{
        opacity: shouldShow ? 1 : 0,
        y: shouldShow ? 0 : 50
      }}
      transition={{
        duration: 0.4,
        ease: "easeOut"
      }}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-16 items-center max-w-4xl mx-auto mb-16">
        {/* Left Side - Step Number */}
        <div className="flex flex-col items-center justify-center">
          <div className="w-32 h-32 bg-accent rounded-full flex items-center justify-center shadow-lg">
            <span className="text-primary text-4xl font-bold font-heading">
              {number}
            </span>
          </div>
        </div>

        {/* Right Side - Content */}
        <div className="flex flex-col justify-center space-y-6">
          <h3 className="text-secondary font-heading font-bold text-4xl">
            {title}
          </h3>
          <p className="text-secondary/80 text-xl leading-relaxed">
            {description}
          </p>
        </div>
      </div>
    </motion.div>
  );
};

const Process = () => {
  const sectionRef = useRef(null);
  const [titleVisible, setTitleVisible] = useState(false);
  const [titleShouldDisappear, setTitleShouldDisappear] = useState(false);
  const [visibleCards, setVisibleCards] = useState([]);

  // Process steps data
  const processSteps = [
    {
      id: 1,
      number: "01",
      title: "Initial Proposal",
      description: "We discuss your vision and I provide a detailed proposal with timeline and pricing."
    },
    {
      id: 2,
      number: "02",
      title: "Design & Planning",
      description: "I create wireframes and mockups, then plan the technical architecture for your project."
    },
    {
      id: 3,
      number: "03",
      title: "Development",
      description: "I build your project using modern technologies, keeping you updated throughout the process."
    },
    {
      id: 4,
      number: "04",
      title: "Launch & Support",
      description: "I deploy your project and provide ongoing support to ensure everything runs smoothly."
    }
  ];

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const rect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const sectionTop = rect.top;

      // Show title when section reaches center of viewport
      if (sectionTop <= windowHeight * 0.5 && !titleVisible) {
        setTitleVisible(true);
      }

      // Hide title when section scrolls up past center
      if (sectionTop > windowHeight * 0.5 && titleVisible) {
        setTitleVisible(false);
        setTitleShouldDisappear(false);
        setVisibleCards([]);
      }

      // After title is visible, start the card sequence based on scroll
      if (titleVisible) {
        const scrolledIntoSection = Math.abs(sectionTop - windowHeight * 0.5);
        const scrollProgress = scrolledIntoSection / (windowHeight * 0.3); // Each 30vh of scroll = 1 step

        // Title stays for first 100vh of scroll, then disappears
        if (scrollProgress > 1 && !titleShouldDisappear) {
          setTitleShouldDisappear(true);
        }

        // Show cards progressively after title disappears
        if (titleShouldDisappear) {
          const cardProgress = (scrollProgress - 1) / 0.8; // Each 80vh = 1 card
          const cardsToShow = Math.min(Math.floor(cardProgress) + 1, processSteps.length);

          const newVisibleCards = [];
          for (let i = 0; i < cardsToShow; i++) {
            newVisibleCards.push(i);
          }
          setVisibleCards(newVisibleCards);
        }
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Check initial position

    return () => window.removeEventListener('scroll', handleScroll);
  }, [titleVisible, titleShouldDisappear]);

  return (
    <div ref={sectionRef} className="bg-background py-16" data-section="process" style={{ minHeight: '400vh' }}>
      <div className="w-3/4 mx-auto px-6">

        {/* Process Title - appears centered, stays, then disappears */}
        <div className="min-h-screen flex items-center justify-center">
          <AnimatedTitle
            title="Process"
            triggerPoint={0}
            className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
            containerClassName={`text-center transition-opacity duration-500 ${
              titleVisible && !titleShouldDisappear ? 'opacity-100' : 'opacity-0'
            }`}
          />
        </div>

        {/* Process Cards - appear one by one in the same center position */}
        <div className="space-y-0">
          {processSteps.map((step, index) => (
            <div key={step.id} className="min-h-screen flex items-center justify-center">
              <ProcessCard
                number={step.number}
                title={step.title}
                description={step.description}
                shouldShow={visibleCards.includes(index)}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Process;
