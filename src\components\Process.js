"use client";

import { motion } from 'framer-motion';
import { useRef, useState, useEffect } from 'react';
import AnimatedTitle from './AnimatedTitle';

const ProcessCard = ({ number, title, description }) => {
  const cardRef = useRef(null);
  const [hasAnimatedIn, setHasAnimatedIn] = useState(false);
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (!cardRef.current) return;

      const rect = cardRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Element's top position relative to viewport
      const elementTop = rect.top;

      // Trigger point: when element is centered in viewport (50% down from top) - same as Services
      const triggerPoint = windowHeight * 0.5;

      // Show animation when scrolling down and element comes into view
      if (elementTop <= triggerPoint && !hasAnimatedIn) {
        setHasAnimatedIn(true);
        setShouldShow(true);
      }

      // Hide animation when scrolling back up past the same trigger point
      if (elementTop > triggerPoint && hasAnimatedIn) {
        setHasAnimatedIn(false);
        setShouldShow(false);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    // Check initial position
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasAnimatedIn]);

  return (
    <motion.div
      ref={cardRef}
      className="mx-auto mb-16"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{
        opacity: shouldShow ? 1 : 0,
        scale: shouldShow ? 1 : 0.95
      }}
      transition={{
        duration: 0.2,
        ease: "easeOut"
      }}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-16 items-center max-w-4xl mx-auto">
        {/* Left Side - Step Number */}
        <div className="flex flex-col items-center justify-center">
          <div className="w-32 h-32 bg-accent rounded-full flex items-center justify-center shadow-lg">
            <span className="text-primary text-4xl font-bold font-heading">
              {number}
            </span>
          </div>
        </div>

        {/* Right Side - Content */}
        <div className="flex flex-col justify-center space-y-6">
          <h3 className="text-secondary font-heading font-bold text-4xl">
            {title}
          </h3>
          <p className="text-secondary/80 text-xl leading-relaxed">
            {description}
          </p>
        </div>
      </div>
    </motion.div>
  );
};

const Process = () => {
  // Process steps data
  const processSteps = [
    {
      id: 1,
      number: "01",
      title: "Initial Proposal",
      description: "We discuss your vision and I provide a detailed proposal with timeline and pricing."
    },
    {
      id: 2,
      number: "02",
      title: "Design & Planning",
      description: "I create wireframes and mockups, then plan the technical architecture for your project."
    },
    {
      id: 3,
      number: "03",
      title: "Development",
      description: "I build your project using modern technologies, keeping you updated throughout the process."
    },
    {
      id: 4,
      number: "04",
      title: "Launch & Support",
      description: "I deploy your project and provide ongoing support to ensure everything runs smoothly."
    }
  ];

  return (
    <div className="bg-background py-16" data-section="process">
      <div className="w-3/4 mx-auto px-6">

        {/* Animated Process Title - behaves like Services title */}
        <AnimatedTitle
          title="Process"
          triggerPoint={0.5}
          className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
          containerClassName="text-center mb-16"
        />

        {/* Process Cards - behave like Service cards */}
        {processSteps.map((step) => (
          <ProcessCard
            key={step.id}
            number={step.number}
            title={step.title}
            description={step.description}
          />
        ))}
      </div>
    </div>
  );
};

export default Process;
