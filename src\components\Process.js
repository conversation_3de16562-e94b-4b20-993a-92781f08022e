"use client";

import { useRef, useState, useEffect } from 'react';

import AnimatedFixedTitle from './AnimatedFixedTitle';
import ProcessCard from './ProcessCard';

const Process = () => {
  const sectionRef = useRef(null);
  const [hasAnimatedIn, setHasAnimatedIn] = useState(false);
  const [titleVisible, setTitleVisible] = useState(false);
  const [scrollEffects, setScrollEffects] = useState({
    opacity: 1,
    blur: 0,
    scale: 1
  });
  const [cardScrollProgress, setCardScrollProgress] = useState(0);

  // Process steps data - moved up to calculate dynamic height
  const processSteps = [
    {
      id: 1,
      number: "01",
      title: "Initial Proposal",
      description: "We discuss your vision and I provide a detailed proposal with timeline and pricing."
    },
    {
      id: 2,
      number: "02",
      title: "Design & Planning",
      description: "I create wireframes and mockups, then plan the technical architecture for your project."
    },
    {
      id: 3,
      number: "03",
      title: "Development",
      description: "I build your project using modern technologies, keeping you updated throughout the process."
    },
    {
      id: 4,
      number: "04",
      title: "Launch & Support",
      description: "I deploy your project and provide ongoing support to ensure everything runs smoothly."
    }
  ];





  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const processRect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Get Projects section (previous section) to determine when to show Process title
      const projectsSection = document.querySelector('[data-section="projects"]');
      if (!projectsSection) return;

      const projectsRect = projectsSection.getBoundingClientRect();
      const projectsBottom = projectsRect.bottom;
      const projectsTop = projectsRect.top;

      // Show title when projects section is almost scrolled past
      const triggerPoint = windowHeight * 0.3;
      const projectsAlmostGone = projectsBottom <= triggerPoint;
      const projectsStartedScrolling = projectsTop <= windowHeight * 0.8;

      const shouldShowTitle = projectsAlmostGone && projectsStartedScrolling;

      if (shouldShowTitle && !hasAnimatedIn) {
        setHasAnimatedIn(true);
        setTitleVisible(true);
      }

      if (!shouldShowTitle && hasAnimatedIn) {
        setHasAnimatedIn(false);
        setTitleVisible(false);
      }

      // Calculate scroll-driven effects for title and cards
      if (titleVisible) {
        const processTop = processRect.top;
        const triggerOffset = windowHeight * 0.1;

        if (processTop <= triggerOffset) {
          const scrolledIntoProcess = Math.abs(processTop - triggerOffset);
          // Total scroll distance for the entire process section
          const totalSectionScrollDistance = windowHeight * 5; // 500vh section needs 5x window height
          const rawScrollProgress = Math.min(1, scrolledIntoProcess / totalSectionScrollDistance);

          // Title effects - fade out slower
          const titleScrollProgress = Math.min(1, scrolledIntoProcess / (windowHeight * 4));
          const opacity = Math.max(0, 1 - (titleScrollProgress * 1.5)); // Slower fade
          const blur = titleScrollProgress * 6; // Less blur
          const scale = Math.max(0.9, 1 - (titleScrollProgress * 0.2)); // Less scaling

          setScrollEffects({ opacity, blur, scale });

          // Card scroll progress - only starts after title has been visible for a while
          // Cards should start appearing when we're about 30% into the section scroll
          const cardStartDelay = 0.3; // 30% delay before cards start
          const cardProgress = Math.max(0, (rawScrollProgress - cardStartDelay) / (1 - cardStartDelay));
          setCardScrollProgress(cardProgress);
        } else {
          setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
          setCardScrollProgress(0);
        }
      } else {
        setCardScrollProgress(0);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasAnimatedIn, titleVisible]);

  return (
    <>
      {/* Fixed/Centered Title with scroll-driven effects */}
      <div className="fixed inset-0 flex items-center justify-center pointer-events-none z-0">
        <AnimatedFixedTitle
          title="Process"
          titleVisible={titleVisible}
          scrollEffects={scrollEffects}
          className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
          containerClassName=""
        />
      </div>

      {/* Process Section - Fixed height to ensure proper scroll behavior */}
      <section
        ref={sectionRef}
        data-section="process"
        className="bg-background relative z-10"
        style={{ height: '500vh' }} // Increased height to ensure 4th card is fully visible with new timing
      >
        {/* Process Cards Container */}
        <div className="sticky top-0 h-screen w-full overflow-hidden">
          <div className="h-full flex items-center justify-center relative bg-background">
            <ProcessCard
              processSteps={processSteps}
              scrollProgress={cardScrollProgress}
            />
          </div>
        </div>
      </section>
    </>
  );
};

export default Process;
