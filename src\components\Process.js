"use client";

import { useRef, useState, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import AnimatedFixedTitle from './AnimatedFixedTitle';
import ProcessCard from './ProcessCard';

const Process = () => {
  const sectionRef = useRef(null);
  const [hasAnimatedIn, setHasAnimatedIn] = useState(false);
  const [titleVisible, setTitleVisible] = useState(false);
  const [scrollEffects, setScrollEffects] = useState({
    opacity: 1,
    blur: 0,
    scale: 1
  });

  // Process steps data - moved up to calculate dynamic height
  const processSteps = [
    {
      id: 1,
      number: "01",
      title: "Initial Proposal",
      description: "We discuss your vision and I provide a detailed proposal with timeline and pricing."
    },
    {
      id: 2,
      number: "02",
      title: "Design & Planning",
      description: "I create wireframes and mockups, then plan the technical architecture for your project."
    },
    {
      id: 3,
      number: "03",
      title: "Development",
      description: "I build your project using modern technologies, keeping you updated throughout the process."
    },
    {
      id: 4,
      number: "04",
      title: "Launch & Support",
      description: "I deploy your project and provide ongoing support to ensure everything runs smoothly."
    }
  ];

  // Calculate dynamic section height based on card timing
  const calculateSectionHeight = () => {
    const delayOffset = 0.35; // Same as in ProcessCard
    const totalSteps = processSteps.length;
    const availableProgress = 1 - delayOffset;
    const cardDuration = availableProgress / totalSteps;

    // Last card ends at delayOffset + availableProgress = 1.0
    // But we need extra buffer to ensure the last card is fully visible and settled
    const bufferProgress = 0.15; // 15% extra buffer after last card
    const totalProgress = 1 + bufferProgress;

    // Convert to viewport heights (each 1.0 progress = 100vh of scroll)
    const sectionHeight = totalProgress * 100;

    return `${sectionHeight}vh`;
  };

  // Use Framer Motion's useScroll for smooth scroll tracking
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const processRect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Get Projects section (previous section) to determine when to show Process title
      const projectsSection = document.querySelector('[data-section="projects"]');
      if (!projectsSection) return;

      const projectsRect = projectsSection.getBoundingClientRect();
      const projectsBottom = projectsRect.bottom;
      const projectsTop = projectsRect.top;

      // Show title when projects section is almost scrolled past
      const triggerPoint = windowHeight * 0.3;
      const projectsAlmostGone = projectsBottom <= triggerPoint;
      const projectsStartedScrolling = projectsTop <= windowHeight * 0.8;

      const shouldShowTitle = projectsAlmostGone && projectsStartedScrolling;

      if (shouldShowTitle && !hasAnimatedIn) {
        setHasAnimatedIn(true);
        setTitleVisible(true);
      }

      if (!shouldShowTitle && hasAnimatedIn) {
        setHasAnimatedIn(false);
        setTitleVisible(false);
      }

      // Calculate scroll-driven effects for title - make it fade out slower
      if (titleVisible) {
        const processTop = processRect.top;
        const triggerOffset = windowHeight * 0.1;
        
        if (processTop <= triggerOffset) {
          const scrolledIntoProcess = Math.abs(processTop - triggerOffset);
          // Increased distance so title fades out slower, allowing all cards to be seen
          const sectionScrollDistance = windowHeight * 4; // Increased from 2.5
          const scrollProgress = Math.min(1, scrolledIntoProcess / sectionScrollDistance);

          const opacity = Math.max(0, 1 - (scrollProgress * 1.5)); // Slower fade
          const blur = scrollProgress * 6; // Less blur
          const scale = Math.max(0.9, 1 - (scrollProgress * 0.2)); // Less scaling

          setScrollEffects({ opacity, blur, scale });
        } else {
          setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
        }
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasAnimatedIn, titleVisible]);

  return (
    <>
      {/* Fixed/Centered Title with scroll-driven effects */}
      <div className="fixed inset-0 flex items-center justify-center pointer-events-none z-0">
        <AnimatedFixedTitle
          title="Process"
          titleVisible={titleVisible}
          scrollEffects={scrollEffects}
          className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
          containerClassName=""
        />
      </div>

      {/* Process Section - Dynamic height based on card timing */}
      <section
        ref={sectionRef}
        data-section="process"
        className="bg-background relative z-10"
        style={{ height: calculateSectionHeight() }} // Dynamic height calculation
      >
        {/* Process Cards Container */}
        <div className="sticky top-0 h-screen w-full overflow-hidden">
          <div className="h-full flex items-center justify-center relative bg-background">
            <ProcessCard
              processSteps={processSteps}
              scrollProgress={scrollYProgress}
            />
          </div>
        </div>
      </section>
    </>
  );
};

export default Process;
