"use client";

import { motion, useTransform } from 'framer-motion';

const ProcessCard = ({ processSteps, scrollProgress }) => {
  const totalSteps = processSteps.length;
  
  // Create card animations that start AFTER title begins to fade
  const createCardAnimation = (index) => {
    // Cards should only start appearing when we're 35% into the section
    // This gives the Process title much more time to be visible first
    const delayOffset = 0.35; // 35% delay before any cards appear
    const availableProgress = 1 - delayOffset; // Remaining 65% for cards

    // Each card gets equal portion of the remaining progress
    const cardDuration = availableProgress / totalSteps;
    const cardStart = delayOffset + (index * cardDuration);
    const cardEnd = delayOffset + ((index + 1) * cardDuration);
    const cardCenter = (cardStart + cardEnd) / 2;

    // Sharp, instant transitions - no slow smooth scrolling
    const opacity = useTransform(scrollProgress,
      [
        cardStart - 0.01, // Extremely small fade-in zone
        cardStart, // Instantly reach full opacity
        cardEnd - 0.01, // Stay visible until very near end
        cardEnd // Instant fade out
      ],
      [0, 1, 1, 0]
    );

    // Minimal scale effect to avoid slowing down perception
    const scale = useTransform(scrollProgress,
      [
        cardStart,
        cardCenter,
        cardEnd
      ],
      [0.99, 1, 0.99]
    );

    // Faster, more responsive vertical movement with less overlap
    const y = useTransform(scrollProgress,
      [
        cardStart - 0.01,
        cardStart + 0.02, // Quick settle to center
        cardEnd - 0.02, // Start exit early
        cardEnd
      ],
      [40, 0, 0, -40] // Reduced movement distance and faster exit
    );

    return { opacity, scale, y };
  };

  return (
    <div className="w-full h-full flex items-center justify-center relative overflow-hidden">
      <div className="w-full max-w-6xl mx-auto px-8 relative h-full flex items-center">
        
        {/* Render all cards with fast, responsive animations */}
        {processSteps.map((step, index) => {
          const { opacity, scale, y } = createCardAnimation(index);
          
          return (
            <motion.div
              key={step.id}
              className="absolute inset-0 grid grid-cols-2 gap-16 items-center"
              style={{
                opacity,
                scale,
                y
              }}
              // Remove spring animations to avoid slowing down scroll feel
            >
              {/* Left Side - Step Numbers/Icons */}
              <div className="flex flex-col items-center justify-center">
                <div className="w-32 h-32 bg-accent rounded-full flex items-center justify-center shadow-lg">
                  <span className="text-primary text-4xl font-bold font-heading">
                    {step.number}
                  </span>
                </div>
              </div>

              {/* Right Side - Content */}
              <div className="flex flex-col justify-center space-y-6">
                <h3 className="text-secondary font-heading font-bold text-4xl">
                  {step.title}
                </h3>

                <p className="text-secondary/80 text-xl leading-relaxed max-w-lg">
                  {step.description}
                </p>
              </div>
            </motion.div>
          );
        })}
        
        {/* Simple progress indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3">
          {processSteps.map((_, index) => {
            const delayOffset = 0.2;
            const availableProgress = 1 - delayOffset;
            const cardDuration = availableProgress / totalSteps;
            const cardStart = delayOffset + (index * cardDuration);
            const cardEnd = delayOffset + ((index + 1) * cardDuration);
            
            const indicatorOpacity = useTransform(scrollProgress,
              [cardStart, cardEnd],
              [1, 1]
            );
            
            const isActive = useTransform(scrollProgress, (progress) => {
              return progress >= cardStart && progress < cardEnd ? 1 : 0.3;
            });
            
            return (
              <motion.div
                key={index}
                className="w-3 h-3 rounded-full bg-accent"
                style={{ opacity: isActive }}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ProcessCard;
